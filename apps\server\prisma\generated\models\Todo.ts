
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Todo` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Todo
 * 
 */
export type TodoModel = runtime.Types.Result.DefaultSelection<Prisma.$TodoPayload>

export type AggregateTodo = {
  _count: TodoCountAggregateOutputType | null
  _avg: TodoAvgAggregateOutputType | null
  _sum: TodoSumAggregateOutputType | null
  _min: TodoMinAggregateOutputType | null
  _max: TodoMaxAggregateOutputType | null
}

export type TodoAvgAggregateOutputType = {
  id: number | null
}

export type TodoSumAggregateOutputType = {
  id: number | null
}

export type TodoMinAggregateOutputType = {
  id: number | null
  text: string | null
  completed: boolean | null
}

export type TodoMaxAggregateOutputType = {
  id: number | null
  text: string | null
  completed: boolean | null
}

export type TodoCountAggregateOutputType = {
  id: number
  text: number
  completed: number
  _all: number
}


export type TodoAvgAggregateInputType = {
  id?: true
}

export type TodoSumAggregateInputType = {
  id?: true
}

export type TodoMinAggregateInputType = {
  id?: true
  text?: true
  completed?: true
}

export type TodoMaxAggregateInputType = {
  id?: true
  text?: true
  completed?: true
}

export type TodoCountAggregateInputType = {
  id?: true
  text?: true
  completed?: true
  _all?: true
}

export type TodoAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Todo to aggregate.
   */
  where?: Prisma.TodoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Todos to fetch.
   */
  orderBy?: Prisma.TodoOrderByWithRelationInput | Prisma.TodoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TodoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Todos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Todos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Todos
  **/
  _count?: true | TodoCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: TodoAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: TodoSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TodoMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TodoMaxAggregateInputType
}

export type GetTodoAggregateType<T extends TodoAggregateArgs> = {
      [P in keyof T & keyof AggregateTodo]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTodo[P]>
    : Prisma.GetScalarType<T[P], AggregateTodo[P]>
}




export type TodoGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TodoWhereInput
  orderBy?: Prisma.TodoOrderByWithAggregationInput | Prisma.TodoOrderByWithAggregationInput[]
  by: Prisma.TodoScalarFieldEnum[] | Prisma.TodoScalarFieldEnum
  having?: Prisma.TodoScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TodoCountAggregateInputType | true
  _avg?: TodoAvgAggregateInputType
  _sum?: TodoSumAggregateInputType
  _min?: TodoMinAggregateInputType
  _max?: TodoMaxAggregateInputType
}

export type TodoGroupByOutputType = {
  id: number
  text: string
  completed: boolean
  _count: TodoCountAggregateOutputType | null
  _avg: TodoAvgAggregateOutputType | null
  _sum: TodoSumAggregateOutputType | null
  _min: TodoMinAggregateOutputType | null
  _max: TodoMaxAggregateOutputType | null
}

type GetTodoGroupByPayload<T extends TodoGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TodoGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TodoGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TodoGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TodoGroupByOutputType[P]>
      }
    >
  > 



export type TodoWhereInput = {
  AND?: Prisma.TodoWhereInput | Prisma.TodoWhereInput[]
  OR?: Prisma.TodoWhereInput[]
  NOT?: Prisma.TodoWhereInput | Prisma.TodoWhereInput[]
  id?: Prisma.IntFilter<"Todo"> | number
  text?: Prisma.StringFilter<"Todo"> | string
  completed?: Prisma.BoolFilter<"Todo"> | boolean
}

export type TodoOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  text?: Prisma.SortOrder
  completed?: Prisma.SortOrder
}

export type TodoWhereUniqueInput = Prisma.AtLeast<{
  id?: number
  AND?: Prisma.TodoWhereInput | Prisma.TodoWhereInput[]
  OR?: Prisma.TodoWhereInput[]
  NOT?: Prisma.TodoWhereInput | Prisma.TodoWhereInput[]
  text?: Prisma.StringFilter<"Todo"> | string
  completed?: Prisma.BoolFilter<"Todo"> | boolean
}, "id">

export type TodoOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  text?: Prisma.SortOrder
  completed?: Prisma.SortOrder
  _count?: Prisma.TodoCountOrderByAggregateInput
  _avg?: Prisma.TodoAvgOrderByAggregateInput
  _max?: Prisma.TodoMaxOrderByAggregateInput
  _min?: Prisma.TodoMinOrderByAggregateInput
  _sum?: Prisma.TodoSumOrderByAggregateInput
}

export type TodoScalarWhereWithAggregatesInput = {
  AND?: Prisma.TodoScalarWhereWithAggregatesInput | Prisma.TodoScalarWhereWithAggregatesInput[]
  OR?: Prisma.TodoScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TodoScalarWhereWithAggregatesInput | Prisma.TodoScalarWhereWithAggregatesInput[]
  id?: Prisma.IntWithAggregatesFilter<"Todo"> | number
  text?: Prisma.StringWithAggregatesFilter<"Todo"> | string
  completed?: Prisma.BoolWithAggregatesFilter<"Todo"> | boolean
}

export type TodoCreateInput = {
  text: string
  completed?: boolean
}

export type TodoUncheckedCreateInput = {
  id?: number
  text: string
  completed?: boolean
}

export type TodoUpdateInput = {
  text?: Prisma.StringFieldUpdateOperationsInput | string
  completed?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TodoUncheckedUpdateInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  text?: Prisma.StringFieldUpdateOperationsInput | string
  completed?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TodoCreateManyInput = {
  id?: number
  text: string
  completed?: boolean
}

export type TodoUpdateManyMutationInput = {
  text?: Prisma.StringFieldUpdateOperationsInput | string
  completed?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TodoUncheckedUpdateManyInput = {
  id?: Prisma.IntFieldUpdateOperationsInput | number
  text?: Prisma.StringFieldUpdateOperationsInput | string
  completed?: Prisma.BoolFieldUpdateOperationsInput | boolean
}

export type TodoCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  text?: Prisma.SortOrder
  completed?: Prisma.SortOrder
}

export type TodoAvgOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type TodoMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  text?: Prisma.SortOrder
  completed?: Prisma.SortOrder
}

export type TodoMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  text?: Prisma.SortOrder
  completed?: Prisma.SortOrder
}

export type TodoSumOrderByAggregateInput = {
  id?: Prisma.SortOrder
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}



export type TodoSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  text?: boolean
  completed?: boolean
}, ExtArgs["result"]["todo"]>

export type TodoSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  text?: boolean
  completed?: boolean
}, ExtArgs["result"]["todo"]>

export type TodoSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  text?: boolean
  completed?: boolean
}, ExtArgs["result"]["todo"]>

export type TodoSelectScalar = {
  id?: boolean
  text?: boolean
  completed?: boolean
}

export type TodoOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "text" | "completed", ExtArgs["result"]["todo"]>

export type $TodoPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Todo"
  objects: {}
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: number
    text: string
    completed: boolean
  }, ExtArgs["result"]["todo"]>
  composites: {}
}

export type TodoGetPayload<S extends boolean | null | undefined | TodoDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TodoPayload, S>

export type TodoCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TodoFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: TodoCountAggregateInputType | true
  }

export interface TodoDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Todo'], meta: { name: 'Todo' } }
  /**
   * Find zero or one Todo that matches the filter.
   * @param {TodoFindUniqueArgs} args - Arguments to find a Todo
   * @example
   * // Get one Todo
   * const todo = await prisma.todo.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TodoFindUniqueArgs>(args: Prisma.SelectSubset<T, TodoFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TodoClient<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Todo that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TodoFindUniqueOrThrowArgs} args - Arguments to find a Todo
   * @example
   * // Get one Todo
   * const todo = await prisma.todo.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TodoFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TodoFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TodoClient<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Todo that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TodoFindFirstArgs} args - Arguments to find a Todo
   * @example
   * // Get one Todo
   * const todo = await prisma.todo.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TodoFindFirstArgs>(args?: Prisma.SelectSubset<T, TodoFindFirstArgs<ExtArgs>>): Prisma.Prisma__TodoClient<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Todo that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TodoFindFirstOrThrowArgs} args - Arguments to find a Todo
   * @example
   * // Get one Todo
   * const todo = await prisma.todo.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TodoFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TodoFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TodoClient<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Todos that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TodoFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Todos
   * const todos = await prisma.todo.findMany()
   * 
   * // Get first 10 Todos
   * const todos = await prisma.todo.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const todoWithIdOnly = await prisma.todo.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TodoFindManyArgs>(args?: Prisma.SelectSubset<T, TodoFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Todo.
   * @param {TodoCreateArgs} args - Arguments to create a Todo.
   * @example
   * // Create one Todo
   * const Todo = await prisma.todo.create({
   *   data: {
   *     // ... data to create a Todo
   *   }
   * })
   * 
   */
  create<T extends TodoCreateArgs>(args: Prisma.SelectSubset<T, TodoCreateArgs<ExtArgs>>): Prisma.Prisma__TodoClient<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Todos.
   * @param {TodoCreateManyArgs} args - Arguments to create many Todos.
   * @example
   * // Create many Todos
   * const todo = await prisma.todo.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TodoCreateManyArgs>(args?: Prisma.SelectSubset<T, TodoCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Todos and returns the data saved in the database.
   * @param {TodoCreateManyAndReturnArgs} args - Arguments to create many Todos.
   * @example
   * // Create many Todos
   * const todo = await prisma.todo.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Todos and only return the `id`
   * const todoWithIdOnly = await prisma.todo.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TodoCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TodoCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Todo.
   * @param {TodoDeleteArgs} args - Arguments to delete one Todo.
   * @example
   * // Delete one Todo
   * const Todo = await prisma.todo.delete({
   *   where: {
   *     // ... filter to delete one Todo
   *   }
   * })
   * 
   */
  delete<T extends TodoDeleteArgs>(args: Prisma.SelectSubset<T, TodoDeleteArgs<ExtArgs>>): Prisma.Prisma__TodoClient<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Todo.
   * @param {TodoUpdateArgs} args - Arguments to update one Todo.
   * @example
   * // Update one Todo
   * const todo = await prisma.todo.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TodoUpdateArgs>(args: Prisma.SelectSubset<T, TodoUpdateArgs<ExtArgs>>): Prisma.Prisma__TodoClient<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Todos.
   * @param {TodoDeleteManyArgs} args - Arguments to filter Todos to delete.
   * @example
   * // Delete a few Todos
   * const { count } = await prisma.todo.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TodoDeleteManyArgs>(args?: Prisma.SelectSubset<T, TodoDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Todos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TodoUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Todos
   * const todo = await prisma.todo.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TodoUpdateManyArgs>(args: Prisma.SelectSubset<T, TodoUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Todos and returns the data updated in the database.
   * @param {TodoUpdateManyAndReturnArgs} args - Arguments to update many Todos.
   * @example
   * // Update many Todos
   * const todo = await prisma.todo.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Todos and only return the `id`
   * const todoWithIdOnly = await prisma.todo.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TodoUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TodoUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Todo.
   * @param {TodoUpsertArgs} args - Arguments to update or create a Todo.
   * @example
   * // Update or create a Todo
   * const todo = await prisma.todo.upsert({
   *   create: {
   *     // ... data to create a Todo
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Todo we want to update
   *   }
   * })
   */
  upsert<T extends TodoUpsertArgs>(args: Prisma.SelectSubset<T, TodoUpsertArgs<ExtArgs>>): Prisma.Prisma__TodoClient<runtime.Types.Result.GetResult<Prisma.$TodoPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Todos.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TodoCountArgs} args - Arguments to filter Todos to count.
   * @example
   * // Count the number of Todos
   * const count = await prisma.todo.count({
   *   where: {
   *     // ... the filter for the Todos we want to count
   *   }
   * })
  **/
  count<T extends TodoCountArgs>(
    args?: Prisma.Subset<T, TodoCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TodoCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Todo.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TodoAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TodoAggregateArgs>(args: Prisma.Subset<T, TodoAggregateArgs>): Prisma.PrismaPromise<GetTodoAggregateType<T>>

  /**
   * Group by Todo.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TodoGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TodoGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TodoGroupByArgs['orderBy'] }
      : { orderBy?: TodoGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TodoGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTodoGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Todo model
 */
readonly fields: TodoFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Todo.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TodoClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Todo model
 */
export interface TodoFieldRefs {
  readonly id: Prisma.FieldRef<"Todo", 'Int'>
  readonly text: Prisma.FieldRef<"Todo", 'String'>
  readonly completed: Prisma.FieldRef<"Todo", 'Boolean'>
}
    

// Custom InputTypes
/**
 * Todo findUnique
 */
export type TodoFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * Filter, which Todo to fetch.
   */
  where: Prisma.TodoWhereUniqueInput
}

/**
 * Todo findUniqueOrThrow
 */
export type TodoFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * Filter, which Todo to fetch.
   */
  where: Prisma.TodoWhereUniqueInput
}

/**
 * Todo findFirst
 */
export type TodoFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * Filter, which Todo to fetch.
   */
  where?: Prisma.TodoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Todos to fetch.
   */
  orderBy?: Prisma.TodoOrderByWithRelationInput | Prisma.TodoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Todos.
   */
  cursor?: Prisma.TodoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Todos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Todos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Todos.
   */
  distinct?: Prisma.TodoScalarFieldEnum | Prisma.TodoScalarFieldEnum[]
}

/**
 * Todo findFirstOrThrow
 */
export type TodoFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * Filter, which Todo to fetch.
   */
  where?: Prisma.TodoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Todos to fetch.
   */
  orderBy?: Prisma.TodoOrderByWithRelationInput | Prisma.TodoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Todos.
   */
  cursor?: Prisma.TodoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Todos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Todos.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Todos.
   */
  distinct?: Prisma.TodoScalarFieldEnum | Prisma.TodoScalarFieldEnum[]
}

/**
 * Todo findMany
 */
export type TodoFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * Filter, which Todos to fetch.
   */
  where?: Prisma.TodoWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Todos to fetch.
   */
  orderBy?: Prisma.TodoOrderByWithRelationInput | Prisma.TodoOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Todos.
   */
  cursor?: Prisma.TodoWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Todos from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Todos.
   */
  skip?: number
  distinct?: Prisma.TodoScalarFieldEnum | Prisma.TodoScalarFieldEnum[]
}

/**
 * Todo create
 */
export type TodoCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * The data needed to create a Todo.
   */
  data: Prisma.XOR<Prisma.TodoCreateInput, Prisma.TodoUncheckedCreateInput>
}

/**
 * Todo createMany
 */
export type TodoCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Todos.
   */
  data: Prisma.TodoCreateManyInput | Prisma.TodoCreateManyInput[]
}

/**
 * Todo createManyAndReturn
 */
export type TodoCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * The data used to create many Todos.
   */
  data: Prisma.TodoCreateManyInput | Prisma.TodoCreateManyInput[]
}

/**
 * Todo update
 */
export type TodoUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * The data needed to update a Todo.
   */
  data: Prisma.XOR<Prisma.TodoUpdateInput, Prisma.TodoUncheckedUpdateInput>
  /**
   * Choose, which Todo to update.
   */
  where: Prisma.TodoWhereUniqueInput
}

/**
 * Todo updateMany
 */
export type TodoUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Todos.
   */
  data: Prisma.XOR<Prisma.TodoUpdateManyMutationInput, Prisma.TodoUncheckedUpdateManyInput>
  /**
   * Filter which Todos to update
   */
  where?: Prisma.TodoWhereInput
  /**
   * Limit how many Todos to update.
   */
  limit?: number
}

/**
 * Todo updateManyAndReturn
 */
export type TodoUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * The data used to update Todos.
   */
  data: Prisma.XOR<Prisma.TodoUpdateManyMutationInput, Prisma.TodoUncheckedUpdateManyInput>
  /**
   * Filter which Todos to update
   */
  where?: Prisma.TodoWhereInput
  /**
   * Limit how many Todos to update.
   */
  limit?: number
}

/**
 * Todo upsert
 */
export type TodoUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * The filter to search for the Todo to update in case it exists.
   */
  where: Prisma.TodoWhereUniqueInput
  /**
   * In case the Todo found by the `where` argument doesn't exist, create a new Todo with this data.
   */
  create: Prisma.XOR<Prisma.TodoCreateInput, Prisma.TodoUncheckedCreateInput>
  /**
   * In case the Todo was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TodoUpdateInput, Prisma.TodoUncheckedUpdateInput>
}

/**
 * Todo delete
 */
export type TodoDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
  /**
   * Filter which Todo to delete.
   */
  where: Prisma.TodoWhereUniqueInput
}

/**
 * Todo deleteMany
 */
export type TodoDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Todos to delete
   */
  where?: Prisma.TodoWhereInput
  /**
   * Limit how many Todos to delete.
   */
  limit?: number
}

/**
 * Todo without action
 */
export type TodoDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Todo
   */
  select?: Prisma.TodoSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Todo
   */
  omit?: Prisma.TodoOmit<ExtArgs> | null
}
