{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc --noEmit", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot src/index.ts", "start": "bun run dist/index.js", "db:push": "prisma db push --schema ./prisma/schema", "db:studio": "prisma studio", "db:generate": "prisma generate --schema ./prisma/schema", "db:migrate": "prisma migrate dev"}, "prisma": {"schema": "./schema"}, "dependencies": {"dotenv": "^16.4.7", "zod": "^4.0.2", "@orpc/server": "^1.5.0", "@orpc/client": "^1.5.0", "hono": "^4.8.2", "@prisma/client": "^6.9.0", "better-auth": "^1.2.10"}, "devDependencies": {"tsdown": "^0.12.9", "typescript": "^5.8.2", "@types/bun": "^1.2.6", "prisma": "^6.9.0"}}